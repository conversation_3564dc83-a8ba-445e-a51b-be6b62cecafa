*, :before, :after {
  box-sizing: border-box;
  border: 0 solid;
}

* {
  margin: 0;
  padding: 0;
}

body {
  -webkit-font-smoothing: antialiased;
  font-family: system-ui, sans-serif;
  line-height: 1.5;
}

input {
  background-color: #0000;
  padding: .5rem;
  font-family: inherit;
}

input:not([class]) {
  border: 1px solid #bfc2c7;
}

input:focus {
  outline: 2px solid #0000;
}

input:disabled {
  cursor: not-allowed;
  opacity: .5;
}

code {
  font-family: monospace;
  font-size: 1em;
}

.m-100 {
  margin: 25rem;
}

.f-1 {
  flex: 1;
}

.p-r {
  position: relative;
}
