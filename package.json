{"name": "snipco", "version": "0.1.0", "description": "A tool that turns code into beautiful images for social sharing.", "keywords": ["generate", "export", "share", "snippet", "image", "code", "yumma<PERSON>s"], "homepage": "https://snipco.netlify.app", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "private": true, "type": "module", "scripts": {"astro": "astro", "build": "astro build && yummacss build", "dev": "concurrently \"astro dev\" \"yummacss watch\"", "format": "prettier --write .", "preview": "astro preview"}, "dependencies": {"@expressive-code/plugin-line-numbers": "^0.41.3", "@ianvs/prettier-plugin-sort-imports": "^4.6.1", "astro": "^5.12.9", "astro-expressive-code": "^0.41.3", "html2canvas": "^1.4.1"}, "devDependencies": {"concurrently": "^9.2.0", "prettier": "^3.6.2", "yummacss": "^3.1.0"}}