# [snipco](snipco.netlify.app)

A tool that turns code into beautiful images for social sharing.

## Getting started

Snipco takes your code and generates awesome, custom images that you can easily share on social media, in presentations, or in documentation.

> _Snipco was made [with <PERSON><PERSON> CSS](https://github.com/with-yummacss/)_

### Installing

Clone the repository and install dependencies:

```bash
npm install
# or
pnpm install
```

To start developing:

```bash
npm run dev
# or
pnpm dev
```

## Built with

- [yummacss](https://www.npmjs.com/package/yummacss)
- [radix-ui](https://www.npmjs.com/package/radix-ui)
- [next](https://www.npmjs.com/package/next)
- [typescript](https://www.npmjs.com/package/typescript)

## License

This project is licensed under the [MIT License](LICENSE)
